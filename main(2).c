#define _CRT_SECURE_NO_WARNINGS
#define _WINSOCK_DEPRECATED_NO_WARNINGS
#define _WIN32_WINNT 0x0600 // ֧�� Vista �����ϣ�ȷ�� inet_ntop ����

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <winsock2.h>
#include <ws2tcpip.h> // ֧�� inet_ntop
#include <windows.h>
#include <time.h>
#include <stdint.h>
#include <errno.h>

#pragma comment(lib, "ws2_32.lib")

// ����
#define INET_ADDRSTRLEN 16
#define BUFFER_SIZE 1024
#define DNS_PORT 53
#define MAX_DOMAIN_LENGTH 255
#define IPMAXSIZE 16
#define MAX_LOCAL_ENTRIES 1500
#define MAX_ID_ENTRIES 128 // ��СΪ 32���ʺ���̨����
#define LOCAL_ADDRESS "0.0.0.0" // �������нӿ�
#define DEFAULT_DNS_SERVER "*******" // ���� DNS ��������ַ
#define NOT_FOUND -1
#define CONFIG_FILE "dnsrelay.txt"
#define ID_TIMEOUT_SECONDS 5 // ID ��Ŀ��ʱʱ�䣨�룩
#define RESET_TTL 120//Ĭ�����õ�TTL
#define ASCIISIZE 128

// DNS ����ͷ�ṹ��
typedef struct {
    uint16_t id;
    uint16_t flags;
    uint16_t question_count;
    uint16_t answer_count;
    uint16_t authority_count;
    uint16_t additional_count;
} DnsHeader;

//// ���ر���Ŀ
//typedef struct {
//    char* domain;
//    char* ip;
//} DnsEntry;

// ���� DNS ����Ŀ
typedef struct trie {
    char* IPAddress; // IP ��ַ
    struct trie** nextEleArray; // ������Ӧ����һ��Ԫ�ص�����
}Trie, * TriePtr;

// ID ת������Ŀ
typedef struct {
    uint16_t old_id;
    SOCKADDR_IN client_addr;
    int completed;
    time_t timestamp; // ����ʱ������ڳ�ʱ����
} IdMapping;

//// ������Ŀ
//typedef struct {
//    char* domain;
//    char* ip;
//    int ttl;
//} CacheEntry;

//cache��Ԫ
typedef struct record {
    char* domain;
    char* ip;
    int ttl;

    struct record* next;
}Record, * RecordPtr;

//cache
typedef struct cache {
    RecordPtr records;
    int capacity;
    int size;
}Cache, * CachePtr;

// ȫ�ֱ���
static TriePtr dnsRoot = NULL; // DNS ������������
static int localTableCount = 0;
//static CacheEntry cache[256];
//static int cacheCount = 0;
static CachePtr dnsCache = NULL;
static IdMapping idTable[MAX_ID_ENTRIES];
static int idTableCount = 0;
static char domainBuffer[MAX_DOMAIN_LENGTH];

// ��ʼ������
static int initializeNetwork(SOCKET* localSocket, SOCKET* serverSocket, SOCKADDR_IN* localAddr, SOCKADDR_IN* serverAddr) {
    printf("Designed by Haokun Tian.\n");

    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        fprintf(stderr, "WSAStartupʧ��: %d\n", WSAGetLastError());
        return -1;
    }

    *localSocket = socket(AF_INET, SOCK_DGRAM, 0);
    *serverSocket = socket(AF_INET, SOCK_DGRAM, 0);
    if (*localSocket == INVALID_SOCKET || *serverSocket == INVALID_SOCKET) {
        fprintf(stderr, "Socket����ʧ��: %d\n", WSAGetLastError());
        WSACleanup();
        return -1;
    }

    unsigned long nonBlocking = 1;
    if (ioctlsocket(*localSocket, FIONBIO, &nonBlocking) || ioctlsocket(*serverSocket, FIONBIO, &nonBlocking)) {
        fprintf(stderr, "���÷�����ʧ��: %d\n", WSAGetLastError());
        closesocket(*localSocket);
        closesocket(*serverSocket);
        WSACleanup();
        return -1;
    }

    localAddr->sin_family = AF_INET;
    localAddr->sin_port = htons(DNS_PORT);
    localAddr->sin_addr.s_addr = INADDR_ANY;
    serverAddr->sin_family = AF_INET;
    serverAddr->sin_port = htons(DNS_PORT);
    serverAddr->sin_addr.s_addr = inet_addr(DEFAULT_DNS_SERVER);

    if (bind(*localSocket, (SOCKADDR*)localAddr, sizeof(*localAddr)) != 0) {
        fprintf(stderr, "�󶨶˿� %d ʧ��: %d\n", DNS_PORT, WSAGetLastError());
        closesocket(*localSocket);
        closesocket(*serverSocket);
        WSACleanup();
        return -1;
    }

    printf("���ڰ󶨶˿� %d...\n", DNS_PORT);
    Sleep(100);
    printf("�󶨳ɹ����ȴ��ͻ�������\n");
    return 0;
}

// ���ر��� DNS ��
//static int loadLocalTable(const char* filename) {
//    FILE* file = fopen(filename, "r");
//    if (!file) {
//        fprintf(stderr, "�޷��� %s: %s\n", filename, strerror(errno));
//        return 0;
//    }
//
//    char line[512];
//    localTableCount = 0;
//    while (fgets(line, sizeof(line), file) && localTableCount < MAX_LOCAL_ENTRIES) {
//        line[strcspn(line, "\r\n")] = '\0';
//        char* ip = strtok(line, " \t");
//        char* domain = strtok(NULL, " \t");
//        if (ip && domain) {
//            localTable[localTableCount].ip = _strdup(ip);
//            localTable[localTableCount].domain = _strdup(domain);
//            if (!localTable[localTableCount].ip || !localTable[localTableCount].domain) {
//                fprintf(stderr, "���ر��ڴ����ʧ�ܡ�\n");
//                free(localTable[localTableCount].ip);
//                free(localTable[localTableCount].domain);
//                continue;
//            }
//            localTableCount++;
//        }
//    }
//
//    fclose(file);
//    printf("�� %s ������ %d ����¼��\n", filename, localTableCount);
//    return localTableCount;
//}

// ���ر��� DNS ��
static void loadLocalTable(const char* filename) {
	FILE* file = fopen(filename, "r");
	if (!file) {
		fprintf(stderr, "�޷��� %s: %s\n", filename, strerror(errno));
		return ;
	}

	dnsRoot = (TriePtr)malloc(sizeof(Trie));
	dnsRoot->IPAddress = NULL;
	dnsRoot->nextEleArray = (TriePtr*)malloc(sizeof(TriePtr) * ASCIISIZE);
	memset(dnsRoot->nextEleArray, 0, sizeof(TriePtr) * ASCIISIZE);

    //��ÿ��������������
    TriePtr curNode;
	char domain[MAX_DOMAIN_LENGTH];
    char ip[IPMAXSIZE];
    char select[2];
    localTableCount = 0;
    while (fscanf(file, "%s %s", ip, domain) > 0)
    {
        //��������
		curNode = dnsRoot;
		int len = strlen(domain);
        for (int i = 0; i < len; i++)
        {
			if (curNode->nextEleArray[domain[i]] == NULL) {
				curNode->nextEleArray[domain[i]] = (TriePtr)malloc(sizeof(Trie));
				curNode->nextEleArray[domain[i]]->IPAddress = NULL;
				curNode->nextEleArray[domain[i]]->nextEleArray = (TriePtr*)malloc(sizeof(TriePtr) * ASCIISIZE);
				memset(curNode->nextEleArray[domain[i]]->nextEleArray, 0, sizeof(TriePtr) * ASCIISIZE);
			}
			curNode = curNode->nextEleArray[domain[i]];
        }

		//����IP��ַ,IP������ʱ
        if (curNode->IPAddress == NULL)
        {
            curNode->IPAddress = (char*)malloc(sizeof(char) * (strlen(ip) + 1));
            memcpy(curNode->IPAddress, ip, strlen(ip) + 1);
        }
		//IP��ַ�Ѵ������뵱ǰIP��ַ��ͬ��������ͻ
		else if (strcmp(curNode->IPAddress, ip) != 0)
		{
            printf("\t%s: former IP: %s, new IP: %s. Overwriting.\n", domain, curNode->IPAddress, ip);
            free(curNode->IPAddress);
            curNode->IPAddress = (char*)malloc(sizeof(char) * (strlen(ip) + 1));
            memcpy(curNode->IPAddress, ip, strlen(ip) + 1);
		}
		localTableCount++;
    }
	fclose(file);

	printf("�� %s ������ %d ����¼��\n", filename, localTableCount);
}

// ��ȡ����
static int extractDomain(const char* packet, int length, char* domain) {
    if (length < sizeof(DnsHeader) + 1) {
        return -1;
    }

    int offset = sizeof(DnsHeader);
    int domainPos = 0;
    while (offset < length && packet[offset] != 0) {
        int len = (unsigned char)packet[offset];
        if (len > 63 || offset + len + 1 >= length || domainPos + len + 1 >= MAX_DOMAIN_LENGTH) {
            return -1;
        }
        offset++;
        for (int i = 0; i < len; i++) {
            domain[domainPos++] = packet[offset++];
        }
        if (packet[offset] != 0) {
            domain[domainPos++] = '.';
        }
    }
    domain[domainPos] = '\0';
    return offset + 5;
}

//// ���ұ��ر�
//static int searchLocalTable(const char* domain) {
//    for (int i = 0; i < localTableCount; i++) {
//        if (localTable[i].domain && strcmp(localTable[i].domain, domain) == 0) {
//            return i;
//        }
//    }
//    return NOT_FOUND;
//}

// ���ұ��ر�
static char* searchLocalTable(const char* domain) {
    if (dnsRoot == NULL)
        return NULL;

    TriePtr curNode = dnsRoot;
    int len = (int)strlen(domain);
    for (int i = 0; i < len; i++)
    {
        if (curNode->nextEleArray[domain[i]] == NULL)
        {
            return NULL;
        }

        curNode = curNode->nextEleArray[domain[i]];
    }

    return curNode->IPAddress;
}

//����cache
static void createCache() {
	dnsCache = (CachePtr)malloc(sizeof(Cache));
	dnsCache->capacity = 256;
	dnsCache->size = 0;

	dnsCache->records = (RecordPtr)malloc(sizeof(Record));
	dnsCache->records->domain = NULL;
	dnsCache->records->ip = NULL;
	dnsCache->records->next = NULL;

}

//// ���һ���
//static int searchCache(const char* domain) {
//    for (int i = 0; i < cacheCount; i++) {
//        if (cache[i].domain && strcmp(cache[i].domain, domain) == 0 && cache[i].ttl > 0) {
//            return i;
//        }
//    }
//    return NOT_FOUND;
//}

// ���һ���
static char* searchCache(const char* domain) {
    if (dnsCache == NULL || dnsCache->records == NULL) 
        return NULL;
	char* ip = NULL;

    RecordPtr preNode = dnsCache->records;
    RecordPtr curNode = NULL;

    while (preNode->next != NULL)
    {
        if (strcmp(preNode->next->domain, domain) == 0)
        {
            ip = preNode->next->ip;

            curNode = preNode->next;
            curNode->ttl = RESET_TTL;
            preNode->next = curNode->next;
            curNode->next = dnsCache->records->next;
            dnsCache->records->next = curNode;

            break;
        }
        preNode = preNode->next;
    }

	return ip;
}

//// ���뻺��
//static void insertCache(const char* domain, const char* ip, int ttl) {
//    if (cacheCount >= 256) {
//        free(cache[0].domain);
//        free(cache[0].ip);
//        memmove(&cache[0], &cache[1], sizeof(CacheEntry) * (cacheCount - 1));
//        cacheCount--;
//    }
//
//    cache[cacheCount].domain = _strdup(domain);
//    cache[cacheCount].ip = _strdup(ip);
//    cache[cacheCount].ttl = ttl;
//    if (!cache[cacheCount].domain || !cache[cacheCount].ip) {
//        free(cache[cacheCount].domain);
//        free(cache[cacheCount].ip);
//        return;
//    }
//    cacheCount++;
//}

//���뻺��
static void insertCache(const char* domain, char* ip, int ttl) {
        // don't find it
        RecordPtr curNode = (RecordPtr)malloc(sizeof(Record));
        curNode->domain = (char*)malloc(sizeof(char) * MAX_DOMAIN_LENGTH);
        memcpy(curNode->domain, domain, strlen(domain) + 1);
        curNode->ip = (char*)malloc(sizeof(char) * IPMAXSIZE);
        memcpy(curNode->ip, ip, strlen(ip) + 1);
        curNode->ttl = ttl;
        curNode->next = dnsCache->records->next;
        dnsCache->records->next = curNode;

        dnsCache->size++;
        printf("Cache added URL=%s, IP=%s\n", domain, ip);

        //��������capacity�Ļ���
	    RecordPtr preNode = dnsCache->records;
        for (int count = 1; preNode->next != NULL; count++)
        {
            if (count >= dnsCache->capacity)
                break;
		    preNode = preNode->next;
        }
        while (preNode->next != NULL) {
		    dnsCache->size--;
			curNode = preNode->next;
			preNode->next = curNode->next;
			free(curNode->domain);
			free(curNode->ip);
			free(curNode);
        }
    
}

//// ���»��� TTL
//static void updateCacheTTL() {
//    for (int i = 0; i < cacheCount; i++) {
//        if (cache[i].ttl > 0) {
//            cache[i].ttl--;
//        }
//        if (cache[i].ttl <= 0 && cache[i].domain) {
//            free(cache[i].domain);
//            free(cache[i].ip);
//            cache[i].domain = NULL;
//            cache[i].ip = NULL;
//        }
//    }
//}

// ���»��� TTL
static void updateCacheTTL() {
    if (dnsCache == NULL || dnsCache->records == NULL) return;

    RecordPtr preNode = dnsCache->records;
    RecordPtr curNode = preNode->next;

    while (curNode != NULL) {
        curNode->ttl--;
        if (curNode->ttl <= 0) {
            // �ͷŵ�ǰ�ڵ�
            preNode->next = curNode->next;
            free(curNode->domain);
            free(curNode->ip);
            free(curNode);
            dnsCache->size--;
            curNode = preNode->next;
        }
        else {
            preNode = curNode;
            curNode = curNode->next;
        }
    }
}

// ������ʱ ID ��Ŀ
static void cleanupIdTable() {
    time_t now = time(NULL);
    int removed = 0;
    for (int i = 0; i < idTableCount; i++) {
        if (!idTable[i].completed && (now - idTable[i].timestamp) > ID_TIMEOUT_SECONDS) {
            // ��ʱδ�����Ŀ�����Ϊ�����
            idTable[i].completed = 1;
            removed++;
        }
    }
    /*if (removed > 0) {
        printf("���� %d ����ʱ ID ��Ŀ��\n", removed);
    }*/
}

// ������ ID
static uint16_t generateNewId(uint16_t oldId, SOCKADDR_IN clientAddr) {
    // ���������������Ŀ
    for (int i = 0; i < idTableCount; i++) {
        if (idTable[i].completed) {
            idTable[i].old_id = oldId;
            idTable[i].client_addr = clientAddr;
            idTable[i].completed = 0;
            idTable[i].timestamp = time(NULL);
            //printf("���� ID ���� %d��ԭʼ ID %u��\n", i, oldId);
            return (uint16_t)i;
        }
    }

    // ��������Ŀ
    if (idTableCount >= MAX_ID_ENTRIES) {
        //fprintf(stderr, "ID ������������������ʱ��Ŀ��\n");
        cleanupIdTable();
        // �ٴμ���Ƿ��п�����Ŀ
        for (int i = 0; i < idTableCount; i++) {
            if (idTable[i].completed) {
                idTable[i].old_id = oldId;
                idTable[i].client_addr = clientAddr;
                idTable[i].completed = 0;
                idTable[i].timestamp = time(NULL);
                //printf("����������� ID ���� %d��ԭʼ ID %u��\n", i, oldId);
                return (uint16_t)i;
            }
        }
        //fprintf(stderr, "�޷������� ID������ԭʼ ID %u��\n", oldId);
        return oldId;
    }

    idTable[idTableCount].old_id = oldId;
    idTable[idTableCount].client_addr = clientAddr;
    idTable[idTableCount].completed = 0;
    idTable[idTableCount].timestamp = time(NULL);
    //printf("������ ID ���� %d��ԭʼ ID %u��\n", idTableCount, oldId);
    return (uint16_t)idTableCount++;
}

// ����ԭʼ ID
static int findOriginalId(uint16_t newId, uint16_t* oldId, SOCKADDR_IN* clientAddr) {
    if (newId < MAX_ID_ENTRIES && !idTable[newId].completed) {
        *oldId = idTable[newId].old_id;
        *clientAddr = idTable[newId].client_addr;
        idTable[newId].completed = 1;
        //printf("�ҵ� ID ���� %u��ԭʼ ID %u�����Ϊ����ɡ�\n", newId, *oldId);
        return 0;
    }
    //printf("ID ���� %u ��Ч������ɣ������ǳٵ�Ӧ��\n", newId);
    return -1;
}

// ���� DNS ��Ӧ
static int buildResponse(char* response, const char* request, int requestLen, const char* ip, int* responseLen) {
    if (requestLen < sizeof(DnsHeader) || !ip) {
        return -1;
    }

    memcpy(response, request, requestLen);
    DnsHeader* header = (DnsHeader*)response;
    header->flags = htons(0x8180);
    header->answer_count = strcmp(ip, "0.0.0.0") == 0 ? 0 : htons(1);

    if (strcmp(ip, "0.0.0.0") == 0) {
        *responseLen = requestLen;
        return 0;
    }

    int offset = requestLen;
    uint16_t namePtr = htons(0xc00c);
    memcpy(response + offset, &namePtr, 2);
    offset += 2;

    uint16_t typeA = htons(1);
    memcpy(response + offset, &typeA, 2);
    offset += 2;

    uint16_t classIN = htons(1);
    memcpy(response + offset, &classIN, 2);
    offset += 2;

    uint32_t ttl = htonl(120);
    memcpy(response + offset, &ttl, 4);
    offset += 4;

    uint16_t rdLength = htons(4);
    memcpy(response + offset, &rdLength, 2);
    offset += 2;

    uint32_t ipAddr = inet_addr(ip);
    if (ipAddr == INADDR_NONE) {
        return -1;
    }
    memcpy(response + offset, &ipAddr, 4);
    offset += 4;

    *responseLen = offset;
    return 0;
}

// ��־���
static void logQuery(uint16_t id, const char* domain, const char* ip, const char* source, SOCKADDR_IN* clientAddr) {
    SYSTEMTIME time;
    GetLocalTime(&time);
    char clientIp[INET_ADDRSTRLEN];
    if (inet_ntop(AF_INET, &clientAddr->sin_addr, clientIp, INET_ADDRSTRLEN) == NULL) {
        strcpy(clientIp, inet_ntoa(clientAddr->sin_addr));
    }
    printf("[%02d:%02d:%02d.%03d] ID=%u, �ͻ���=%s, ����=%s, IP=%s, ��Դ=%s\n",
        time.wHour, time.wMinute, time.wSecond, time.wMilliseconds, id, clientIp, domain, ip, source);
}

//�������ر�
static void freeTrieNode(TriePtr node)
{
    if (node->IPAddress != NULL)
        free(node->IPAddress);
    for (int i = 0; i < ASCIISIZE; i++) {
        if (node->nextEleArray[i] != NULL) {
            freeTrieNode(node->nextEleArray[i]);
        }
    }
    free(node->nextEleArray);
    free(node);
}

//����Cache
static void freeCache(CachePtr dnsCache) {
    RecordPtr curNode;
    while (dnsCache->records->next != NULL) {
        curNode = dnsCache->records->next;
        dnsCache->records->next = curNode->next;
        free(curNode->domain);
        free(curNode->ip);
        free(curNode);
    }
    free(dnsCache->records);
    free(dnsCache);
}

// ������Դ
static void cleanup(SOCKET localSocket, SOCKET serverSocket) {
    if (dnsRoot) {
        freeTrieNode(dnsRoot);
        dnsRoot = NULL;
    }
    if (dnsCache) {
        freeCache(dnsCache);
        dnsCache = NULL;
    }
    closesocket(localSocket);
    closesocket(serverSocket);
    WSACleanup();
}

int main() {
    SOCKET localSocket, serverSocket;
    SOCKADDR_IN localAddr, serverAddr;

    // ��ʼ������
    if (initializeNetwork(&localSocket, &serverSocket, &localAddr, &serverAddr) != 0) {
        return 1;
    }

    // ���ر��ر�
    loadLocalTable(CONFIG_FILE);
    if (dnsRoot == NULL) {
        cleanup(localSocket, serverSocket);
        return 1;
    }

	// ��������
	createCache();

    // ��ѭ��
    char request[BUFFER_SIZE];
    char response[BUFFER_SIZE];
    SOCKADDR_IN clientAddr;
    int clientAddrLen = sizeof(clientAddr);

    while (1) {
        updateCacheTTL();
        cleanupIdTable(); // ����������ʱ ID ��Ŀ

        // �����ͻ�������
        int recvLen = recvfrom(localSocket, request, BUFFER_SIZE, 0, (SOCKADDR*)&clientAddr, &clientAddrLen);
        if (recvLen == SOCKET_ERROR) {
            /*if (WSAGetLastError() != WSAEWOULDBLOCK) {
                fprintf(stderr, "����ʧ��: %d\n", WSAGetLastError());
            }*/
            continue;
        }
        if (recvLen < sizeof(DnsHeader)) {
            continue;
        }

        if (extractDomain(request, recvLen, domainBuffer) == -1) {
            continue;
        }

		char* cacheIP = searchCache(domainBuffer);
		char* localIP = searchLocalTable(domainBuffer);
        DnsHeader* header = (DnsHeader*)request;
        uint16_t oldId = ntohs(header->id);
        int responseLen;

        if (cacheIP!=NULL) {
            if (buildResponse(response, request, recvLen, cacheIP, &responseLen) == 0) {
                sendto(localSocket, response, responseLen, 0, (SOCKADDR*)&clientAddr, clientAddrLen);
                logQuery(oldId, domainBuffer, cacheIP, "����", &clientAddr);
            }
        }
        else if (localIP!=NULL) {
            if (buildResponse(response, request, recvLen, localIP, &responseLen) == 0) {
                sendto(localSocket, response, responseLen, 0, (SOCKADDR*)&clientAddr, clientAddrLen);
                logQuery(oldId, domainBuffer, localIP,
                    strcmp(localIP, "0.0.0.0") == 0 ? "����" : "����", &clientAddr);
            }
        }
        else {
            uint16_t newId = generateNewId(oldId, clientAddr);
            header->id = htons(newId);
            int sentBytes = sendto(serverSocket, request, recvLen, 0, (SOCKADDR*)&serverAddr, sizeof(serverAddr));
            if (sentBytes == SOCKET_ERROR) {
                //fprintf(stderr, "ת������ʧ��: %d\n", WSAGetLastError());
                continue;
            }

            // ͬʱ�����ͻ��˺ͷ�������Ӧ
            fd_set readSet;
            FD_ZERO(&readSet);
            FD_SET(localSocket, &readSet);
            FD_SET(serverSocket, &readSet);
            struct timeval timeout = { ID_TIMEOUT_SECONDS, 0 };
            int selectResult = select(0, &readSet, NULL, NULL, &timeout);
            if (selectResult <= 0) {
                //printf("�ⲿ DNS ����ʱ��ID %u��\n", oldId);
                continue;
            }

            if (FD_ISSET(serverSocket, &readSet)) {
                recvLen = recvfrom(serverSocket, response, BUFFER_SIZE, 0, NULL, NULL);
                if (recvLen < sizeof(DnsHeader)) {
                    continue;
                }

                header = (DnsHeader*)response;
                uint16_t responseId = ntohs(header->id);
                uint16_t originalId;
                SOCKADDR_IN originalClient;
                if (findOriginalId(responseId, &originalId, &originalClient) != 0) {
                    continue; // ���Գٵ�����ЧӦ��
                }
                header->id = htons(originalId);

                int offset = sizeof(DnsHeader);
                while (offset < recvLen && response[offset] != 0) {
                    offset += (unsigned char)response[offset] + 1;
                }
                offset += 5;
                int answerCount = ntohs(header->answer_count);
                char ip[16];

                for (int i = 0; i < answerCount && offset + 12 <= recvLen; i++) {
                    if ((unsigned char)response[offset] == 0xc0) {
                        offset += 2;
                    }
                    uint16_t type = ntohs(*(uint16_t*)(response + offset));
                    offset += 4;
                    uint32_t ttl = ntohl(*(uint32_t*)(response + offset));
                    offset += 4;
                    uint16_t rdLength = ntohs(*(uint16_t*)(response + offset));
                    offset += 2;
                    if (type == 1 && rdLength == 4 && offset + 4 <= recvLen) {
                        sprintf(ip, "%d.%d.%d.%d",
                            (unsigned char)response[offset],
                            (unsigned char)response[offset + 1],
                            (unsigned char)response[offset + 2],
                            (unsigned char)response[offset + 3]);
                        insertCache(domainBuffer, ip, ttl);
                        logQuery(originalId, domainBuffer, ip, "�ⲿ", &originalClient);
                    }
                    offset += rdLength;
                }

                sendto(localSocket, response, recvLen, 0, (SOCKADDR*)&originalClient, sizeof(originalClient));
            }
        }
    }

    cleanup(localSocket, serverSocket);
    return 0;
}