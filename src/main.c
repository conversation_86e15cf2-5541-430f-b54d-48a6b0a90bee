#include "../include/common.h"

// 处理DNS查询请求
static int handle_dns_query(socket_t local_socket, socket_t server_socket,
                            const sockaddr_in_t *server_addr)
{
    char request[BUFFER_SIZE];
    char response[BUFFER_SIZE];
    sockaddr_in_t client_addr;
    socklen_t client_addr_len = sizeof(client_addr);

    // 接收DNS查询请求
    int recv_len = network_recvfrom(local_socket, request, BUFFER_SIZE,
                                    &client_addr, &client_addr_len);

    if (recv_len == SOCKET_ERROR_VALUE)
    {
        int error = network_get_last_error();
        if (!network_is_would_block_error(error))
        {
            fprintf(stderr, "recvfrom failed: %s\n", network_error_string(error));
        }
        return DNS_ERROR;
    }

    // 检查数据包长度是否足够
    if (recv_len < sizeof(DnsHeader))
    {
        return DNS_INVALID_PACKET;
    }

    // 从请求中提取域名
    if (extract_domain(request, recv_len, domainBuffer) < 0)
    {
        return DNS_INVALID_PACKET; // 域名提取失败
    }

    // 获取查询ID
    DnsHeader *header = (DnsHeader *)request;
    uint16_t query_id = ntohs(header->id);
    int response_len;

    // 优先级1：检查LRU缓存
    char *cache_ip = search_cache_lru(domainBuffer);
    if (cache_ip != NULL)
    {
        if (build_response(response, request, recv_len,
                           cache_ip, &response_len) == DNS_SUCCESS)
        {
            network_sendto(local_socket, response, response_len,
                           &client_addr, client_addr_len);
            log_query_with_client(query_id, domainBuffer, cache_ip, DNS_SOURCE_CACHE, &client_addr);
        }
        return DNS_SUCCESS;
    }

    // 优先级2：检查字典树本地表
    char *local_ip = search_local_table_trie(domainBuffer);
    if (local_ip != NULL)
    {
        if (build_response(response, request, recv_len,
                           local_ip, &response_len) == DNS_SUCCESS)
        {
            network_sendto(local_socket, response, response_len,
                           &client_addr, client_addr_len);

            DnsSourceType source = is_blocked_domain(local_ip) ? DNS_SOURCE_BLOCKED : DNS_SOURCE_LOCAL;
            log_query_with_client(query_id, domainBuffer, local_ip, source, &client_addr);
        }
        return DNS_SUCCESS;
    }

    // 优先级3：转发到外部DNS服务器
    uint16_t new_id = generate_new_id(query_id, &client_addr);
    header->id = htons(new_id);

    // 转发请求到外部DNS服务器
    if (network_sendto(server_socket, request, recv_len,
                       server_addr, sizeof(*server_addr)) == SOCKET_ERROR_VALUE)
    {
        fprintf(stderr, "Failed to forward request to external DNS\n");
        return DNS_ERROR;
    }

    // 等待外部DNS服务器响应
    if (network_select_timeout(server_socket, 5) <= 0)
    {
        return DNS_TIMEOUT; // 超时
    }

    // 接收外部DNS服务器的响应
    recv_len = network_recvfrom(server_socket, response, BUFFER_SIZE, NULL, NULL);
    if (recv_len < sizeof(DnsHeader))
    {
        return DNS_INVALID_PACKET;
    }

    // 恢复原始ID和客户端地址
    header = (DnsHeader *)response;
    uint16_t response_id = ntohs(header->id);
    uint16_t original_id;
    sockaddr_in_t original_client;

    if (find_original_id(response_id, &original_id, &original_client) != DNS_SUCCESS)
    {
        return DNS_ERROR; // ID映射查找失败
    }
    header->id = htons(original_id); // 恢复原始查询ID

    // 解析响应中的IP地址并加入缓存
    int offset = sizeof(DnsHeader);

    // 跳过问题部分
    while (offset < recv_len && response[offset] != 0)
    {
        offset += (unsigned char)response[offset] + 1;
    }
    offset += 5; // 跳过结束符和查询类型、类别

    // 解析答案部分
    int answer_count = ntohs(header->answer_count);
    char ip[16];

    for (int i = 0; i < answer_count && offset + 12 <= recv_len; i++)
    {
        // 跳过域名指针
        if ((unsigned char)response[offset] == 0xc0)
        {
            offset += 2;
        }

        // 读取记录类型
        uint16_t type = ntohs(*(uint16_t *)(response + offset));
        offset += 4; // 跳过类型和类别

        // 读取TTL
        uint32_t ttl = ntohl(*(uint32_t *)(response + offset));
        offset += 4;

        // 读取数据长度
        uint16_t data_len = ntohs(*(uint16_t *)(response + offset));
        offset += 2;

        // 如果是A记录（IPv4地址）
        if (type == 1 && data_len == 4)
        {
            // 提取IP地址
            sprintf(ip, "%d.%d.%d.%d",
                    (unsigned char)response[offset],
                    (unsigned char)response[offset + 1],
                    (unsigned char)response[offset + 2],
                    (unsigned char)response[offset + 3]);

            // 加入LRU缓存
            insert_cache_lru(domainBuffer, ip, ttl);
            log_query_with_client(original_id, domainBuffer, ip, DNS_SOURCE_EXTERNAL, &original_client);
        }
        offset += data_len;
    }

    // 将响应转发给原始客户端
    network_sendto(local_socket, response, recv_len,
                   &original_client, sizeof(original_client));

    return DNS_SUCCESS;
}

int main(void)
{
    printf("DNS Relay Server - Cross-platform version\n");
    printf("Designed by Haokun Tian.\n");

    // 初始化平台和网络
    if (network_init() != 0)
    {
        fprintf(stderr, "Failed to initialize network\n");
        return 1;
    }

    // 加载本地域名映射表（使用优化的字典树）
    load_local_table_trie(CONFIG_FILE);
    if (dns_trie_root == NULL)
    {
        fprintf(stderr, "Failed to load local DNS table\n");
        cleanup_resources();
        return 1;
    }

    // 创建LRU缓存
    dns_cache = create_lru_cache(MAX_CACHE_ENTRIES);
    if (dns_cache == NULL)
    {
        fprintf(stderr, "Failed to create LRU cache\n");
        cleanup_resources();
        return 1;
    }

    // 创建套接字
    socket_t local_socket = network_create_socket();
    socket_t server_socket = network_create_socket();

    if (local_socket == INVALID_SOCKET_VALUE || server_socket == INVALID_SOCKET_VALUE)
    {
        fprintf(stderr, "Failed to create sockets\n");
        cleanup_resources();
        return 1;
    }

    // 设置非阻塞模式
    if (network_set_nonblocking(local_socket) != 0 ||
        network_set_nonblocking(server_socket) != 0)
    {
        fprintf(stderr, "Failed to set non-blocking mode\n");
        network_close_socket(local_socket);
        network_close_socket(server_socket);
        cleanup_resources();
        return 1;
    }

    // 绑定本地套接字
    if (network_bind_socket(local_socket, LOCAL_ADDRESS, DNS_PORT) != 0)
    {
        fprintf(stderr, "Failed to bind local socket\n");
        network_close_socket(local_socket);
        network_close_socket(server_socket);
        cleanup_resources();
        return 1;
    }

    // 设置外部DNS服务器地址
    sockaddr_in_t server_addr;
    network_setup_address(&server_addr, DEFAULT_DNS_SERVER, DNS_PORT);

    printf("DNS Relay Server started successfully\n");
    printf("Listening on %s:%d\n", LOCAL_ADDRESS, DNS_PORT);
    printf("External DNS: %s:%d\n", DEFAULT_DNS_SERVER, DNS_PORT);

    // 主服务循环
    while (1)
    {
        // 更新LRU缓存TTL和清理超时ID条目
        update_cache_ttl_lru();
        cleanup_id_table();

        // 处理DNS查询
        handle_dns_query(local_socket, server_socket, &server_addr);

        // 短暂休眠以避免CPU占用过高
        platform_sleep(1);
    }

    // 清理资源（实际上不会到达这里）
    network_close_socket(local_socket);
    network_close_socket(server_socket);
    cleanup_resources();

    return 0;
}
