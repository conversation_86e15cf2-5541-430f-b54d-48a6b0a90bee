#include "../include/common.h"

// 全局变量定义
TrieNode *dns_trie_root = NULL;         // 字典树根节点
DnsEntry localTable[MAX_LOCAL_ENTRIES]; // 保留用于兼容性
int localTableCount = 0;
LRUCache *dns_cache = NULL;          // LRU缓存
CacheEntry cache[MAX_CACHE_ENTRIES]; // 保留用于兼容性
int cacheCount = 0;
IdMapping idTable[MAX_ID_ENTRIES];
int idTableCount = 0;
char domainBuffer[MAX_DOMAIN_LENGTH];

// ========== 字典树相关函数 ==========

// 检查字符是否为有效的域名字符
int is_valid_domain_char(char c)
{
    return (c >= 'a' && c <= 'z') ||
           (c >= 'A' && c <= 'Z') ||
           (c >= '0' && c <= '9') ||
           (c == '-') || (c == '.');
}

// 创建字典树节点
TrieNode *create_trie_node(void)
{
    TrieNode *node = (TrieNode *)malloc(sizeof(TrieNode));
    if (!node)
    {
        return NULL;
    }

    node->ip_address = NULL;
    node->children = (TrieNode **)calloc(DOMAIN_CHAR_SIZE, sizeof(TrieNode *));
    if (!node->children)
    {
        free(node);
        return NULL;
    }

    return node;
}

// 释放字典树节点
void free_trie_node(TrieNode *node)
{
    if (!node)
        return;

    // 释放IP地址
    if (node->ip_address)
    {
        free(node->ip_address);
    }

    // 递归释放子节点
    for (int i = 0; i < DOMAIN_CHAR_SIZE; i++)
    {
        if (node->children[i])
        {
            free_trie_node(node->children[i]);
        }
    }

    free(node->children);
    free(node);
}

// 在字典树中查找域名
char *search_local_table_trie(const char *domain)
{
    if (!dns_trie_root || !domain)
        return NULL;

    TrieNode *current = dns_trie_root;
    int len = (int)strlen(domain);

    for (int i = 0; i < len; i++)
    {
        char c = domain[i];

        // 验证字符有效性
        if (!is_valid_domain_char(c))
        {
            return NULL;
        }

        // 检查子节点是否存在
        if (!current->children[(unsigned char)c])
        {
            return NULL;
        }

        current = current->children[(unsigned char)c];
    }

    return current->ip_address;
}

// 从DNS数据包中提取域名
int extract_domain(const char *packet, int length, char *domain)
{
    // 检查数据包长度是否足够
    if (length < sizeof(DnsHeader) + 1)
    {
        return DNS_INVALID_PACKET;
    }

    int offset = sizeof(DnsHeader); // 跳过DNS头部
    int domainPos = 0;

    // 解析域名（DNS标准格式：长度+字符串）
    while (offset < length && packet[offset] != 0)
    {
        int len = (unsigned char)packet[offset];
        // 检查长度是否合法
        if (len > 63 || offset + len + 1 >= length || domainPos + len + 1 >= MAX_DOMAIN_LENGTH)
        {
            return DNS_INVALID_PACKET;
        }
        offset++;

        // 复制域名段
        for (int i = 0; i < len; i++)
        {
            domain[domainPos++] = packet[offset++];
        }

        // 如果不是最后一段，添加点号分隔符
        if (packet[offset] != 0)
        {
            domain[domainPos++] = '.';
        }
    }
    domain[domainPos] = '\0';
    return offset + 1; // 返回域名结束后的偏移量
}

// 在本地域名映射表中查找域名
int search_local_table(const char *domain)
{
    for (int i = 0; i < localTableCount; i++)
    {
        if (localTable[i].domain && strcmp(localTable[i].domain, domain) == 0)
        {
            return i; // 返回找到的索引
        }
    }
    return NOT_FOUND; // 未找到
}

// 在DNS缓存中查找域名
int search_cache(const char *domain)
{
    for (int i = 0; i < cacheCount; i++)
    {
        // 检查域名匹配且TTL有效
        if (cache[i].domain && strcmp(cache[i].domain, domain) == 0 && cache[i].ttl > 0)
        {
            return i; // 返回找到的索引
        }
    }
    return NOT_FOUND; // 未找到或已过期
}

// 向DNS缓存中插入新条目
void insert_cache(const char *domain, const char *ip, int ttl)
{
    // 简单的缓存替换策略：如果满了就覆盖最后一个
    if (cacheCount >= MAX_CACHE_ENTRIES)
    {
        cacheCount = MAX_CACHE_ENTRIES - 1; // 简单替换策略，实际应实现 LRU
    }

    // 分配内存并复制数据
    cache[cacheCount].domain = platform_strdup(domain);
    cache[cacheCount].ip = platform_strdup(ip);
    cache[cacheCount].ttl = ttl;

    // 检查内存分配是否成功
    if (!cache[cacheCount].domain || !cache[cacheCount].ip)
    {
        free(cache[cacheCount].domain);
        free(cache[cacheCount].ip);
        return;
    }
    cacheCount++;
}

// 更新缓存中所有条目的TTL值
void update_cache_ttl(void)
{
    for (int i = 0; i < cacheCount; i++)
    {
        // 递减TTL
        if (cache[i].ttl > 0)
        {
            cache[i].ttl--;
        }

        // 清理过期条目
        if (cache[i].ttl <= 0 && cache[i].domain)
        {
            free(cache[i].domain);
            free(cache[i].ip);
            cache[i].domain = NULL;
            cache[i].ip = NULL;
        }
    }
}

// 清理超时的ID映射条目
void cleanup_id_table(void)
{
    time_t now = time(NULL);
    int removed = 0;

    for (int i = 0; i < idTableCount; i++)
    {
        if (!idTable[i].completed && (now - idTable[i].timestamp) > ID_TIMEOUT_SECONDS)
        {
            // 超时未完成的条目标记为已完成
            idTable[i].completed = 1;
            removed++;
        }
    }

    // 可选：打印清理信息（调试用）
    // if (removed > 0) {
    //     printf("Cleaned up %d timeout ID entries.\n", removed);
    // }
}

// 生成新的查询ID并建立映射关系（优化版本）
uint16_t generate_new_id(uint16_t old_id, const sockaddr_in_t *client_addr)
{
    // 首先查找可重用的条目
    for (int i = 0; i < idTableCount; i++)
    {
        if (idTable[i].completed)
        {
            idTable[i].old_id = old_id;
            idTable[i].client_addr = *client_addr;
            idTable[i].completed = 0;
            idTable[i].timestamp = time(NULL);
            return (uint16_t)i;
        }
    }

    // 如果没有可重用的条目且表已满
    if (idTableCount >= MAX_ID_ENTRIES)
    {
        // 尝试清理超时条目
        cleanup_id_table();

        // 再次查找可重用的条目
        for (int i = 0; i < idTableCount; i++)
        {
            if (idTable[i].completed)
            {
                idTable[i].old_id = old_id;
                idTable[i].client_addr = *client_addr;
                idTable[i].completed = 0;
                idTable[i].timestamp = time(NULL);
                return (uint16_t)i;
            }
        }

        // 如果仍然没有可用条目，返回原ID
        fprintf(stderr, "ID table full, cannot generate new ID.\n");
        return old_id;
    }

    // 创建新条目
    idTable[idTableCount].old_id = old_id;
    idTable[idTableCount].client_addr = *client_addr;
    idTable[idTableCount].completed = 0;
    idTable[idTableCount].timestamp = time(NULL);
    return (uint16_t)idTableCount++;
}

// 根据新ID查找原始ID和客户端地址
int find_original_id(uint16_t new_id, uint16_t *old_id, sockaddr_in_t *client_addr)
{
    // 检查ID是否有效且未完成
    if (new_id < MAX_ID_ENTRIES && idTable[new_id].completed == 0)
    {
        *old_id = idTable[new_id].old_id;           // 返回原始ID
        *client_addr = idTable[new_id].client_addr; // 返回客户端地址
        idTable[new_id].completed = 1;              // 标记为已完成
        return DNS_SUCCESS;                         // 成功
    }
    return DNS_ERROR; // 失败
}

// 构建DNS响应数据包
int build_response(char *response, const char *request, int request_len,
                   const char *ip, int *response_len)
{
    // 参数有效性检查
    if (request_len < sizeof(DnsHeader) || !ip)
    {
        return DNS_INVALID_PACKET;
    }

    // 复制请求数据包作为响应基础
    memcpy(response, request, request_len);
    DnsHeader *header = (DnsHeader *)response;

    // 设置响应标志位（标准查询响应）
    header->flags = htons(0x8180);

    // 设置答案数量（如果IP是0.0.0.0表示域名被屏蔽，答案数为0）
    header->answer_count = strcmp(ip, "0.0.0.0") == 0 ? 0 : htons(1);

    // 如果是屏蔽域名，直接返回无答案的响应
    if (strcmp(ip, "0.0.0.0") == 0)
    {
        *response_len = request_len;
        return DNS_SUCCESS;
    }

    // 构建DNS答案记录
    int offset = request_len;

    // 域名指针（指向问题部分的域名）
    uint16_t namePtr = htons(0xc00c);
    memcpy(response + offset, &namePtr, 2);
    offset += 2;

    // 记录类型（A记录）
    uint16_t typeA = htons(1);
    memcpy(response + offset, &typeA, 2);
    offset += 2;

    // 记录类别（IN - Internet）
    uint16_t classIN = htons(1);
    memcpy(response + offset, &classIN, 2);
    offset += 2;

    // TTL（生存时间）
    uint32_t ttl = htonl(120);
    memcpy(response + offset, &ttl, 4);
    offset += 4;

    // 数据长度（IPv4地址长度为4字节）
    uint16_t rdLength = htons(4);
    memcpy(response + offset, &rdLength, 2);
    offset += 2;

    // IP地址数据
    uint32_t ipAddr = inet_addr(ip);
    if (ipAddr == INADDR_NONE)
    {
        return DNS_INVALID_PACKET; // IP地址格式错误
    }
    memcpy(response + offset, &ipAddr, 4);
    offset += 4;

    *response_len = offset;
    return DNS_SUCCESS;
}

// ========== LRU缓存相关函数 ==========

// 创建LRU缓存
LRUCache *create_lru_cache(int capacity)
{
    LRUCache *cache = (LRUCache *)malloc(sizeof(LRUCache));
    if (!cache)
    {
        return NULL;
    }

    cache->capacity = capacity;
    cache->size = 0;

    // 创建头节点（哨兵节点）
    cache->head = (CacheRecord *)malloc(sizeof(CacheRecord));
    if (!cache->head)
    {
        free(cache);
        return NULL;
    }

    cache->head->domain = NULL;
    cache->head->ip = NULL;
    cache->head->ttl = 0;
    cache->head->next = NULL;

    return cache;
}

// 释放LRU缓存
void free_lru_cache(LRUCache *cache)
{
    if (!cache)
        return;

    CacheRecord *current = cache->head;
    while (current)
    {
        CacheRecord *next = current->next;
        if (current->domain)
            free(current->domain);
        if (current->ip)
            free(current->ip);
        free(current);
        current = next;
    }

    free(cache);
}

// 在LRU缓存中查找域名
char *search_cache_lru(const char *domain)
{
    if (!dns_cache || !dns_cache->head || !domain)
        return NULL;

    CacheRecord *prev = dns_cache->head;
    CacheRecord *current = prev->next;

    while (current)
    {
        if (strcmp(current->domain, domain) == 0 && current->ttl > 0)
        {
            // 找到了，实现LRU：将节点移到链表头部
            char *ip = current->ip;

            // 重置TTL
            current->ttl = RESET_TTL;

            // 从当前位置移除
            prev->next = current->next;

            // 移到头部
            current->next = dns_cache->head->next;
            dns_cache->head->next = current;

            return ip;
        }
        prev = current;
        current = current->next;
    }

    return NULL;
}

// 向LRU缓存插入新条目
void insert_cache_lru(const char *domain, const char *ip, int ttl)
{
    if (!dns_cache || !domain || !ip)
        return;

    // 创建新记录
    CacheRecord *new_record = (CacheRecord *)malloc(sizeof(CacheRecord));
    if (!new_record)
        return;

    new_record->domain = platform_strdup(domain);
    new_record->ip = platform_strdup(ip);
    new_record->ttl = ttl;

    if (!new_record->domain || !new_record->ip)
    {
        free(new_record->domain);
        free(new_record->ip);
        free(new_record);
        return;
    }

    // 插入到链表头部
    new_record->next = dns_cache->head->next;
    dns_cache->head->next = new_record;
    dns_cache->size++;

    printf("Cache added: Domain=%s, IP=%s\n", domain, ip);

    // 如果超过容量，移除最久未使用的条目
    if (dns_cache->size > dns_cache->capacity)
    {
        CacheRecord *prev = dns_cache->head;
        int count = 1;

        // 找到倒数第二个节点
        while (prev->next && count < dns_cache->capacity)
        {
            prev = prev->next;
            count++;
        }

        // 移除后面的所有节点
        while (prev->next)
        {
            CacheRecord *to_remove = prev->next;
            prev->next = to_remove->next;

            free(to_remove->domain);
            free(to_remove->ip);
            free(to_remove);
            dns_cache->size--;
        }
    }
}

// 更新LRU缓存TTL
void update_cache_ttl_lru(void)
{
    if (!dns_cache || !dns_cache->head)
        return;

    CacheRecord *prev = dns_cache->head;
    CacheRecord *current = prev->next;

    while (current)
    {
        current->ttl--;

        if (current->ttl <= 0)
        {
            // 移除过期条目
            prev->next = current->next;
            free(current->domain);
            free(current->ip);
            free(current);
            dns_cache->size--;
            current = prev->next;
        }
        else
        {
            prev = current;
            current = current->next;
        }
    }
}
