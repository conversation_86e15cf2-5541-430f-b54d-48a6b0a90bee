#include "../include/common.h"

// 从配置文件加载本地域名映射表
int load_local_table(const char *filename)
{
    FILE *file = fopen(filename, "r");
    if (!file)
    {
        fprintf(stderr, "Failed to open %s: %s\n", filename, strerror(errno));
        return 0;
    }

    char line[512];
    localTableCount = 1; // 从索引1开始，保留索引0

    while (fgets(line, sizeof(line), file) && localTableCount < MAX_LOCAL_ENTRIES)
    {
        // 移除行尾的换行符
        line[strcspn(line, "\r\n")] = '\0';

        // 跳过空行和注释行
        if (line[0] == '\0' || line[0] == '#')
        {
            continue;
        }

        // 解析IP地址和域名（格式：IP 域名）
        char *ip = strtok(line, " \t");
        char *domain = strtok(NULL, " \t\n");

        if (ip && domain)
        {
            // 分配内存并复制字符串
            localTable[localTableCount].ip = platform_strdup(ip);
            localTable[localTableCount].domain = platform_strdup(domain);

            if (!localTable[localTableCount].ip || !localTable[localTableCount].domain)
            {
                fprintf(stderr, "Memory allocation failed for table entry.\n");
                free(localTable[localTableCount].ip);
                free(localTable[localTableCount].domain);
                continue;
            }
            localTableCount++;
        }
    }

    fclose(file);
    printf("Loaded %d entries from %s.\n", localTableCount - 1, filename);
    return localTableCount - 1;
}

// 使用字典树加载本地域名映射表（优化版本）
void load_local_table_trie(const char *filename)
{
    FILE *file = fopen(filename, "r");
    if (!file)
    {
        fprintf(stderr, "Failed to open %s: %s\n", filename, strerror(errno));
        return;
    }

    // 创建字典树根节点
    dns_trie_root = create_trie_node();
    if (!dns_trie_root)
    {
        fprintf(stderr, "Failed to create trie root node.\n");
        fclose(file);
        return;
    }

    char domain[MAX_DOMAIN_LENGTH];
    char ip[16];
    localTableCount = 0;

    while (fscanf(file, "%15s %254s", ip, domain) == 2)
    {
        // 跳过注释行
        if (ip[0] == '#')
        {
            continue;
        }

        TrieNode *current = dns_trie_root;
        int len = (int)strlen(domain);

        // 验证域名字符
        int valid = 1;
        for (int i = 0; i < len; i++)
        {
            if (!is_valid_domain_char(domain[i]))
            {
                printf("Warning: Invalid character in domain '%s', skipping.\n", domain);
                valid = 0;
                break;
            }
        }

        if (!valid)
            continue;

        // 构建字典树
        for (int i = 0; i < len; i++)
        {
            unsigned char c = (unsigned char)domain[i];

            if (!current->children[c])
            {
                current->children[c] = create_trie_node();
                if (!current->children[c])
                {
                    fprintf(stderr, "Failed to create trie node for character '%c'.\n", c);
                    continue;
                }
            }
            current = current->children[c];
        }

        // 设置IP地址
        if (current->ip_address)
        {
            // IP地址已存在且不同，覆盖并警告
            if (strcmp(current->ip_address, ip) != 0)
            {
                printf("Warning: %s: former IP: %s, new IP: %s. Overwriting.\n",
                       domain, current->ip_address, ip);
                free(current->ip_address);
                current->ip_address = platform_strdup(ip);
            }
        }
        else
        {
            current->ip_address = platform_strdup(ip);
        }

        if (!current->ip_address)
        {
            fprintf(stderr, "Memory allocation failed for IP address.\n");
            continue;
        }

        localTableCount++;
    }

    fclose(file);
    printf("Loaded %d entries from %s using trie structure.\n", localTableCount, filename);
}

// 记录DNS查询日志
void log_query(uint16_t id, const char *domain, const char *ip, DnsSourceType source)
{
    platform_time_t time_info;
    platform_get_time(&time_info);

    printf("[%02d:%02d:%02d.%03d] ID=%u, Domain=%s, IP=%s, Source=%s\n",
           time_info.hour, time_info.minute, time_info.second, time_info.millisecond,
           id, domain, ip, dns_source_to_string(source));
}

// 记录DNS查询日志（包含客户端信息）
void log_query_with_client(uint16_t id, const char *domain, const char *ip,
                           DnsSourceType source, const sockaddr_in_t *client_addr)
{
    platform_time_t time_info;
    platform_get_time(&time_info);

    // 获取客户端IP地址字符串
    char client_ip[INET_ADDRSTRLEN];
    if (inet_ntop(AF_INET, &client_addr->sin_addr, client_ip, INET_ADDRSTRLEN) == NULL)
    {
        // 如果inet_ntop失败，使用inet_ntoa作为备选
        strcpy(client_ip, inet_ntoa(client_addr->sin_addr));
    }

    printf("[%02d:%02d:%02d.%03d] ID=%u, Client=%s, Domain=%s, IP=%s, Source=%s\n",
           time_info.hour, time_info.minute, time_info.second, time_info.millisecond,
           id, client_ip, domain, ip, dns_source_to_string(source));
}

// 清理所有资源
void cleanup_resources(void)
{
    // 释放字典树
    if (dns_trie_root)
    {
        free_trie_node(dns_trie_root);
        dns_trie_root = NULL;
    }

    // 释放LRU缓存
    if (dns_cache)
    {
        free_lru_cache(dns_cache);
        dns_cache = NULL;
    }

    // 释放本地表内存（保留用于兼容性）
    for (int i = 0; i < localTableCount; i++)
    {
        free(localTable[i].domain);
        free(localTable[i].ip);
    }

    // 释放缓存内存（保留用于兼容性）
    for (int i = 0; i < cacheCount; i++)
    {
        free(cache[i].domain);
        free(cache[i].ip);
    }

    // 清理网络资源
    network_cleanup();
}

// DNS来源类型转字符串
const char *dns_source_to_string(DnsSourceType source)
{
    switch (source)
    {
    case DNS_SOURCE_CACHE:
        return "Cache";
    case DNS_SOURCE_LOCAL:
        return "Local";
    case DNS_SOURCE_BLOCKED:
        return "Blocked";
    case DNS_SOURCE_EXTERNAL:
        return "External";
    default:
        return "Unknown";
    }
}

// 检查是否为屏蔽域名
int is_blocked_domain(const char *ip)
{
    return strcmp(ip, "0.0.0.0") == 0;
}
