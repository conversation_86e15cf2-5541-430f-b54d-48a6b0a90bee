# DNS Relay Server - 跨平台版本

## 项目结构

```
dns_relay_crossplatform/
├── include/                 # 头文件
│   ├── platform.h          # 平台抽象层
│   ├── network_wrapper.h   # 网络操作封装
│   ├── dns_types.h         # DNS数据结构
│   └── common.h            # 通用定义
├── src/                    # 核心源代码
│   ├── main.c              # 主程序
│   ├── dns_core.c          # DNS核心逻辑
│   └── config.c            # 配置文件处理
├── platform/               # 平台特定实现
│   ├── windows/            # Windows实现
│   │   ├── platform_win.c
│   │   └── network_win.c
│   └── linux/              # Linux/macOS实现
│       ├── platform_linux.c
│       └── network_linux.c
├── build/                  # 构建输出目录
├── Makefile               # Make构建文件
├── CMakeLists.txt         # CMake构建文件
├── dnsrelay.txt.example   # 配置文件示例
└── README.md              # 项目说明
```

## 编译和安装

### MAC or Linux

```bash
服务器
cd /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/bin

sudo ./dns_relay

用户端
nslookup www.github.com 127.0.0.1
```

### 使用 CMake

```bash
# 删除旧的目录和文件
rmdir build

# 创建构建目录
mkdir build
cd build

# 配置项目
cmake ..（Mac)
cmake -G "MinGW Makefiles" .. （Win)

# 编译项目
cmake --build . --config Release
mingw32-make （Win)

然后进入 bin 运行即可（注意管理员权限）

并且注意切换 Mac/Windows 重新构建 build 文件夹
```


### 网络层差异

- **Windows**: 使用 Winsock2 API
- **Linux/macOS**: 使用标准 BSD Socket API

## 性能特性

- **非阻塞I/O**: 避免服务器阻塞
- **内存缓存**: TTL管理的DNS缓存
- **ID映射**: 支持并发查询处理
- **错误恢复**: 完善的错误处理机制

### 日志级别

程序会输出详细的查询日志，格式：

```
[时:分:秒.毫秒] ID=查询ID, Domain=域名, IP=IP地址, Source=来源
```
