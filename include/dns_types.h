#ifndef DNS_TYPES_H
#define DNS_TYPES_H

#include "platform.h"

// DNS相关常量
#define BUFFER_SIZE 1024
#define MAX_DOMAIN_LENGTH 255
#define MAX_LOCAL_ENTRIES 1500
#define MAX_ID_ENTRIES 256 // 从4096变为256
#define MAX_CACHE_ENTRIES 256
#define NOT_FOUND -1
#define CONFIG_FILE "dnsrelay.txt"
#define ID_TIMEOUT_SECONDS 5 // ID条目超时时间（秒）
#define RESET_TTL 120        // 缓存重置TTL值
#define DOMAIN_CHAR_SIZE 128 // 字典树字符集大小

// DNS数据包头结构体
typedef struct
{
    uint16_t id;               // 查询标识符
    uint16_t flags;            // 标志位
    uint16_t question_count;   // 问题数量
    uint16_t answer_count;     // 答案数量
    uint16_t authority_count;  // 权威记录数量
    uint16_t additional_count; // 附加记录数量
} DnsHeader;

// 字典树节点结构（用于本地域名映射表优化）
typedef struct trie_node
{
    char *ip_address;            // IP地址
    struct trie_node **children; // 子节点数组
} TrieNode;

// 之前的本地域名映射表条目（保留用于兼容性）
typedef struct
{
    char *domain; // 域名
    char *ip;     // IP地址
} DnsEntry;

// LRU缓存记录结构
typedef struct cache_record
{
    char *domain;              // 域名
    char *ip;                  // IP地址
    int ttl;                   // 生存时间
    struct cache_record *next; // 下一个记录（LRU链表）
} CacheRecord;

// LRU缓存结构
typedef struct
{
    CacheRecord *head; // 链表头（最近使用）
    int capacity;      // 缓存容量
    int size;          // 当前大小
} LRUCache;

// DNS缓存条目（保留用于兼容性）
typedef struct
{
    char *domain; // 域名
    char *ip;     // IP地址
    int ttl;      // 生存时间
} CacheEntry;

// ID映射条目（用于处理并发查询）
typedef struct
{
    uint16_t old_id;           // 原始查询ID
    sockaddr_in_t client_addr; // 客户端地址
    int completed;             // 是否已完成
    time_t timestamp;          // 创建时间戳（用于超时清理）
} IdMapping;

// DNS查询来源类型
typedef enum
{
    DNS_SOURCE_CACHE,   // 来自缓存
    DNS_SOURCE_LOCAL,   // 来自本地表
    DNS_SOURCE_BLOCKED, // 被屏蔽
    DNS_SOURCE_EXTERNAL // 来自外部DNS
} DnsSourceType;

// 函数返回状态
typedef enum
{
    DNS_SUCCESS = 0,
    DNS_ERROR = -1,
    DNS_TIMEOUT = -2,
    DNS_INVALID_PACKET = -3,
    DNS_MEMORY_ERROR = -4
} DnsResult;

#endif // DNS_TYPES_H
