#ifndef COMMON_H
#define COMMON_H

#include "platform.h"
#include "dns_types.h"
#include "network_wrapper.h"

// 全局变量声明
extern TrieNode *dns_trie_root;                // 字典树根节点
extern DnsEntry localTable[MAX_LOCAL_ENTRIES]; // 保留用于兼容性
extern int localTableCount;
extern LRUCache *dns_cache;                 // LRU缓存
extern CacheEntry cache[MAX_CACHE_ENTRIES]; // 保留用于兼容性
extern int cacheCount;
extern IdMapping idTable[MAX_ID_ENTRIES];
extern int idTableCount;
extern char domainBuffer[MAX_DOMAIN_LENGTH];

// 核心功能函数声明

// 配置文件处理
int load_local_table(const char *filename);
void load_local_table_trie(const char *filename); // 字典树版本

// 域名解析
int extract_domain(const char *packet, int length, char *domain);

// 字典树相关函数
TrieNode *create_trie_node(void);
void free_trie_node(TrieNode *node);
int is_valid_domain_char(char c);

// 查找功能
int search_local_table(const char *domain);        // 保留用于兼容性
char *search_local_table_trie(const char *domain); // 字典树版本
int search_cache(const char *domain);              // 保留用于兼容性
char *search_cache_lru(const char *domain);        // LRU版本

// LRU缓存管理
LRUCache *create_lru_cache(int capacity);
void free_lru_cache(LRUCache *cache);
void insert_cache_lru(const char *domain, const char *ip, int ttl);
void update_cache_ttl_lru(void);

// 缓存管理（保留用于兼容性）
void insert_cache(const char *domain, const char *ip, int ttl);
void update_cache_ttl(void);

// ID映射管理
uint16_t generate_new_id(uint16_t old_id, const sockaddr_in_t *client_addr);
int find_original_id(uint16_t new_id, uint16_t *old_id, sockaddr_in_t *client_addr);
void cleanup_id_table(void); // 超时清理函数

// DNS响应构建
int build_response(char *response, const char *request, int request_len,
                   const char *ip, int *response_len);

// 日志记录
void log_query(uint16_t id, const char *domain, const char *ip, DnsSourceType source);
void log_query_with_client(uint16_t id, const char *domain, const char *ip,
                           DnsSourceType source, const sockaddr_in_t *client_addr);

// 资源清理
void cleanup_resources(void);

// 工具函数
const char *dns_source_to_string(DnsSourceType source);
int is_blocked_domain(const char *ip);

#endif // COMMON_H
