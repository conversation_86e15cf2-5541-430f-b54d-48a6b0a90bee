# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/opt/homebrew/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/opt/homebrew/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/opt/homebrew/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/opt/homebrew/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/opt/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/opt/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/opt/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/opt/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named dns_relay

# Build rule for target.
dns_relay: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 dns_relay
.PHONY : dns_relay

# fast build rule for target.
dns_relay/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dns_relay.dir/build.make CMakeFiles/dns_relay.dir/build
.PHONY : dns_relay/fast

#=============================================================================
# Target rules for targets named run

# Build rule for target.
run: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 run
.PHONY : run

# fast build rule for target.
run/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run.dir/build.make CMakeFiles/run.dir/build
.PHONY : run/fast

#=============================================================================
# Target rules for targets named clean-all

# Build rule for target.
clean-all: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean-all
.PHONY : clean-all

# fast build rule for target.
clean-all/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/clean-all.dir/build.make CMakeFiles/clean-all.dir/build
.PHONY : clean-all/fast

platform/linux/network_linux.o: platform/linux/network_linux.c.o
.PHONY : platform/linux/network_linux.o

# target to build an object file
platform/linux/network_linux.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dns_relay.dir/build.make CMakeFiles/dns_relay.dir/platform/linux/network_linux.c.o
.PHONY : platform/linux/network_linux.c.o

platform/linux/network_linux.i: platform/linux/network_linux.c.i
.PHONY : platform/linux/network_linux.i

# target to preprocess a source file
platform/linux/network_linux.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dns_relay.dir/build.make CMakeFiles/dns_relay.dir/platform/linux/network_linux.c.i
.PHONY : platform/linux/network_linux.c.i

platform/linux/network_linux.s: platform/linux/network_linux.c.s
.PHONY : platform/linux/network_linux.s

# target to generate assembly for a file
platform/linux/network_linux.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dns_relay.dir/build.make CMakeFiles/dns_relay.dir/platform/linux/network_linux.c.s
.PHONY : platform/linux/network_linux.c.s

platform/linux/platform_linux.o: platform/linux/platform_linux.c.o
.PHONY : platform/linux/platform_linux.o

# target to build an object file
platform/linux/platform_linux.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dns_relay.dir/build.make CMakeFiles/dns_relay.dir/platform/linux/platform_linux.c.o
.PHONY : platform/linux/platform_linux.c.o

platform/linux/platform_linux.i: platform/linux/platform_linux.c.i
.PHONY : platform/linux/platform_linux.i

# target to preprocess a source file
platform/linux/platform_linux.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dns_relay.dir/build.make CMakeFiles/dns_relay.dir/platform/linux/platform_linux.c.i
.PHONY : platform/linux/platform_linux.c.i

platform/linux/platform_linux.s: platform/linux/platform_linux.c.s
.PHONY : platform/linux/platform_linux.s

# target to generate assembly for a file
platform/linux/platform_linux.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dns_relay.dir/build.make CMakeFiles/dns_relay.dir/platform/linux/platform_linux.c.s
.PHONY : platform/linux/platform_linux.c.s

src/config.o: src/config.c.o
.PHONY : src/config.o

# target to build an object file
src/config.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dns_relay.dir/build.make CMakeFiles/dns_relay.dir/src/config.c.o
.PHONY : src/config.c.o

src/config.i: src/config.c.i
.PHONY : src/config.i

# target to preprocess a source file
src/config.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dns_relay.dir/build.make CMakeFiles/dns_relay.dir/src/config.c.i
.PHONY : src/config.c.i

src/config.s: src/config.c.s
.PHONY : src/config.s

# target to generate assembly for a file
src/config.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dns_relay.dir/build.make CMakeFiles/dns_relay.dir/src/config.c.s
.PHONY : src/config.c.s

src/dns_core.o: src/dns_core.c.o
.PHONY : src/dns_core.o

# target to build an object file
src/dns_core.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dns_relay.dir/build.make CMakeFiles/dns_relay.dir/src/dns_core.c.o
.PHONY : src/dns_core.c.o

src/dns_core.i: src/dns_core.c.i
.PHONY : src/dns_core.i

# target to preprocess a source file
src/dns_core.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dns_relay.dir/build.make CMakeFiles/dns_relay.dir/src/dns_core.c.i
.PHONY : src/dns_core.c.i

src/dns_core.s: src/dns_core.c.s
.PHONY : src/dns_core.s

# target to generate assembly for a file
src/dns_core.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dns_relay.dir/build.make CMakeFiles/dns_relay.dir/src/dns_core.c.s
.PHONY : src/dns_core.c.s

src/main.o: src/main.c.o
.PHONY : src/main.o

# target to build an object file
src/main.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dns_relay.dir/build.make CMakeFiles/dns_relay.dir/src/main.c.o
.PHONY : src/main.c.o

src/main.i: src/main.c.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dns_relay.dir/build.make CMakeFiles/dns_relay.dir/src/main.c.i
.PHONY : src/main.c.i

src/main.s: src/main.c.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dns_relay.dir/build.make CMakeFiles/dns_relay.dir/src/main.c.s
.PHONY : src/main.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... clean-all"
	@echo "... run"
	@echo "... dns_relay"
	@echo "... platform/linux/network_linux.o"
	@echo "... platform/linux/network_linux.i"
	@echo "... platform/linux/network_linux.s"
	@echo "... platform/linux/platform_linux.o"
	@echo "... platform/linux/platform_linux.i"
	@echo "... platform/linux/platform_linux.s"
	@echo "... src/config.o"
	@echo "... src/config.i"
	@echo "... src/config.s"
	@echo "... src/dns_core.o"
	@echo "... src/dns_core.i"
	@echo "... src/dns_core.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

