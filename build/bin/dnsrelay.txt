# DNS中继服务器配置文件示例
# 格式: IP地址 域名
# 注释行以 # 开头

# 本地网络服务
************* router.local
*********** gateway.local
******** server.internal

# 开发测试域名
127.0.0.1 localhost.dev
127.0.0.1 test.local
127.0.0.1 dev.example.com

# 屏蔽广告域名 (使用 0.0.0.0)
0.0.0.0 ads.google.com
0.0.0.0 doubleclick.net
0.0.0.0 googleadservices.com
0.0.0.0 googlesyndication.com

# 屏蔽恶意网站
0.0.0.0 malware.example.com
0.0.0.0 phishing.badsite.com
0.0.0.0 virus.dangerous.net

# 自定义DNS服务器映射
******* google-dns.local
******* google-dns-secondary.local
******* cloudflare-dns.local
******* cloudflare-dns-secondary.local

# 公司内部服务
********* mail.company.com
********* wiki.company.com
********* git.company.com
********* jenkins.company.com

# 社交媒体（如需屏蔽）
# 0.0.0.0 facebook.com
# 0.0.0.0 twitter.com
# 0.0.0.0 instagram.com

# 视频网站（如需屏蔽）
# 0.0.0.0 youtube.com
# 0.0.0.0 bilibili.com
# 0.0.0.0 netflix.com
