# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build

# Utility rule file for clean-all.

# Include any custom commands dependencies for this target.
include CMakeFiles/clean-all.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/clean-all.dir/progress.make

CMakeFiles/clean-all:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Cleaning all build files"
	/opt/homebrew/bin/cmake -E remove_directory /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/bin
	/opt/homebrew/bin/cmake -E remove_directory /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles

CMakeFiles/clean-all.dir/codegen:
.PHONY : CMakeFiles/clean-all.dir/codegen

clean-all: CMakeFiles/clean-all
clean-all: CMakeFiles/clean-all.dir/build.make
.PHONY : clean-all

# Rule to build all files generated by this target.
CMakeFiles/clean-all.dir/build: clean-all
.PHONY : CMakeFiles/clean-all.dir/build

CMakeFiles/clean-all.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/clean-all.dir/cmake_clean.cmake
.PHONY : CMakeFiles/clean-all.dir/clean

CMakeFiles/clean-all.dir/depend:
	cd /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles/clean-all.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/clean-all.dir/depend

