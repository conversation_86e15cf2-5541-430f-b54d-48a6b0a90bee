
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/platform/linux/network_linux.c" "CMakeFiles/dns_relay.dir/platform/linux/network_linux.c.o" "gcc" "CMakeFiles/dns_relay.dir/platform/linux/network_linux.c.o.d"
  "/Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/platform/linux/platform_linux.c" "CMakeFiles/dns_relay.dir/platform/linux/platform_linux.c.o" "gcc" "CMakeFiles/dns_relay.dir/platform/linux/platform_linux.c.o.d"
  "/Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/src/config.c" "CMakeFiles/dns_relay.dir/src/config.c.o" "gcc" "CMakeFiles/dns_relay.dir/src/config.c.o.d"
  "/Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/src/dns_core.c" "CMakeFiles/dns_relay.dir/src/dns_core.c.o" "gcc" "CMakeFiles/dns_relay.dir/src/dns_core.c.o.d"
  "/Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/src/main.c" "CMakeFiles/dns_relay.dir/src/main.c.o" "gcc" "CMakeFiles/dns_relay.dir/src/main.c.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
