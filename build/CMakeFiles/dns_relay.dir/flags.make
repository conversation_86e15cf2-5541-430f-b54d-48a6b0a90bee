# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# compile C with /Library/Developer/CommandLineTools/usr/bin/cc
C_DEFINES = -DPLATFORM_MACOS

C_INCLUDES = -I/Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/include

C_FLAGSarm64 =  -Wall -Wextra -std=gnu99 -arch arm64 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk -mmacosx-version-min=13.2

C_FLAGS =  -Wall -Wextra -std=gnu99 -arch arm64 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk -mmacosx-version-min=13.2

