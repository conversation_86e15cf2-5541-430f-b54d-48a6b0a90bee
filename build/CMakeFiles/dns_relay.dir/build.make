# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build

# Include any dependencies generated for this target.
include CMakeFiles/dns_relay.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/dns_relay.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/dns_relay.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/dns_relay.dir/flags.make

CMakeFiles/dns_relay.dir/codegen:
.PHONY : CMakeFiles/dns_relay.dir/codegen

CMakeFiles/dns_relay.dir/src/main.c.o: CMakeFiles/dns_relay.dir/flags.make
CMakeFiles/dns_relay.dir/src/main.c.o: /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/src/main.c
CMakeFiles/dns_relay.dir/src/main.c.o: CMakeFiles/dns_relay.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/dns_relay.dir/src/main.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/dns_relay.dir/src/main.c.o -MF CMakeFiles/dns_relay.dir/src/main.c.o.d -o CMakeFiles/dns_relay.dir/src/main.c.o -c /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/src/main.c

CMakeFiles/dns_relay.dir/src/main.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/dns_relay.dir/src/main.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/src/main.c > CMakeFiles/dns_relay.dir/src/main.c.i

CMakeFiles/dns_relay.dir/src/main.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/dns_relay.dir/src/main.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/src/main.c -o CMakeFiles/dns_relay.dir/src/main.c.s

CMakeFiles/dns_relay.dir/src/dns_core.c.o: CMakeFiles/dns_relay.dir/flags.make
CMakeFiles/dns_relay.dir/src/dns_core.c.o: /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/src/dns_core.c
CMakeFiles/dns_relay.dir/src/dns_core.c.o: CMakeFiles/dns_relay.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/dns_relay.dir/src/dns_core.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/dns_relay.dir/src/dns_core.c.o -MF CMakeFiles/dns_relay.dir/src/dns_core.c.o.d -o CMakeFiles/dns_relay.dir/src/dns_core.c.o -c /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/src/dns_core.c

CMakeFiles/dns_relay.dir/src/dns_core.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/dns_relay.dir/src/dns_core.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/src/dns_core.c > CMakeFiles/dns_relay.dir/src/dns_core.c.i

CMakeFiles/dns_relay.dir/src/dns_core.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/dns_relay.dir/src/dns_core.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/src/dns_core.c -o CMakeFiles/dns_relay.dir/src/dns_core.c.s

CMakeFiles/dns_relay.dir/src/config.c.o: CMakeFiles/dns_relay.dir/flags.make
CMakeFiles/dns_relay.dir/src/config.c.o: /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/src/config.c
CMakeFiles/dns_relay.dir/src/config.c.o: CMakeFiles/dns_relay.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/dns_relay.dir/src/config.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/dns_relay.dir/src/config.c.o -MF CMakeFiles/dns_relay.dir/src/config.c.o.d -o CMakeFiles/dns_relay.dir/src/config.c.o -c /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/src/config.c

CMakeFiles/dns_relay.dir/src/config.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/dns_relay.dir/src/config.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/src/config.c > CMakeFiles/dns_relay.dir/src/config.c.i

CMakeFiles/dns_relay.dir/src/config.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/dns_relay.dir/src/config.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/src/config.c -o CMakeFiles/dns_relay.dir/src/config.c.s

CMakeFiles/dns_relay.dir/platform/linux/platform_linux.c.o: CMakeFiles/dns_relay.dir/flags.make
CMakeFiles/dns_relay.dir/platform/linux/platform_linux.c.o: /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/platform/linux/platform_linux.c
CMakeFiles/dns_relay.dir/platform/linux/platform_linux.c.o: CMakeFiles/dns_relay.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/dns_relay.dir/platform/linux/platform_linux.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/dns_relay.dir/platform/linux/platform_linux.c.o -MF CMakeFiles/dns_relay.dir/platform/linux/platform_linux.c.o.d -o CMakeFiles/dns_relay.dir/platform/linux/platform_linux.c.o -c /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/platform/linux/platform_linux.c

CMakeFiles/dns_relay.dir/platform/linux/platform_linux.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/dns_relay.dir/platform/linux/platform_linux.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/platform/linux/platform_linux.c > CMakeFiles/dns_relay.dir/platform/linux/platform_linux.c.i

CMakeFiles/dns_relay.dir/platform/linux/platform_linux.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/dns_relay.dir/platform/linux/platform_linux.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/platform/linux/platform_linux.c -o CMakeFiles/dns_relay.dir/platform/linux/platform_linux.c.s

CMakeFiles/dns_relay.dir/platform/linux/network_linux.c.o: CMakeFiles/dns_relay.dir/flags.make
CMakeFiles/dns_relay.dir/platform/linux/network_linux.c.o: /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/platform/linux/network_linux.c
CMakeFiles/dns_relay.dir/platform/linux/network_linux.c.o: CMakeFiles/dns_relay.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/dns_relay.dir/platform/linux/network_linux.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/dns_relay.dir/platform/linux/network_linux.c.o -MF CMakeFiles/dns_relay.dir/platform/linux/network_linux.c.o.d -o CMakeFiles/dns_relay.dir/platform/linux/network_linux.c.o -c /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/platform/linux/network_linux.c

CMakeFiles/dns_relay.dir/platform/linux/network_linux.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/dns_relay.dir/platform/linux/network_linux.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/platform/linux/network_linux.c > CMakeFiles/dns_relay.dir/platform/linux/network_linux.c.i

CMakeFiles/dns_relay.dir/platform/linux/network_linux.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/dns_relay.dir/platform/linux/network_linux.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/platform/linux/network_linux.c -o CMakeFiles/dns_relay.dir/platform/linux/network_linux.c.s

# Object files for target dns_relay
dns_relay_OBJECTS = \
"CMakeFiles/dns_relay.dir/src/main.c.o" \
"CMakeFiles/dns_relay.dir/src/dns_core.c.o" \
"CMakeFiles/dns_relay.dir/src/config.c.o" \
"CMakeFiles/dns_relay.dir/platform/linux/platform_linux.c.o" \
"CMakeFiles/dns_relay.dir/platform/linux/network_linux.c.o"

# External object files for target dns_relay
dns_relay_EXTERNAL_OBJECTS =

bin/dns_relay: CMakeFiles/dns_relay.dir/src/main.c.o
bin/dns_relay: CMakeFiles/dns_relay.dir/src/dns_core.c.o
bin/dns_relay: CMakeFiles/dns_relay.dir/src/config.c.o
bin/dns_relay: CMakeFiles/dns_relay.dir/platform/linux/platform_linux.c.o
bin/dns_relay: CMakeFiles/dns_relay.dir/platform/linux/network_linux.c.o
bin/dns_relay: CMakeFiles/dns_relay.dir/build.make
bin/dns_relay: CMakeFiles/dns_relay.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Linking C executable bin/dns_relay"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/dns_relay.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/dns_relay.dir/build: bin/dns_relay
.PHONY : CMakeFiles/dns_relay.dir/build

CMakeFiles/dns_relay.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/dns_relay.dir/cmake_clean.cmake
.PHONY : CMakeFiles/dns_relay.dir/clean

CMakeFiles/dns_relay.dir/depend:
	cd /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles/dns_relay.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/dns_relay.dir/depend

