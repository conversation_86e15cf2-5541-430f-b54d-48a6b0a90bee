# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/dns_relay.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/dns_relay.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/dns_relay.dir/clean
clean: CMakeFiles/run.dir/clean
clean: CMakeFiles/clean-all.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/dns_relay.dir

# All Build rule for target.
CMakeFiles/dns_relay.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dns_relay.dir/build.make CMakeFiles/dns_relay.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dns_relay.dir/build.make CMakeFiles/dns_relay.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles --progress-num=2,3,4,5,6,7 "Built target dns_relay"
.PHONY : CMakeFiles/dns_relay.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/dns_relay.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles 6
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/dns_relay.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles 0
.PHONY : CMakeFiles/dns_relay.dir/rule

# Convenience name for target.
dns_relay: CMakeFiles/dns_relay.dir/rule
.PHONY : dns_relay

# codegen rule for target.
CMakeFiles/dns_relay.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dns_relay.dir/build.make CMakeFiles/dns_relay.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles --progress-num=2,3,4,5,6,7 "Finished codegen for target dns_relay"
.PHONY : CMakeFiles/dns_relay.dir/codegen

# clean rule for target.
CMakeFiles/dns_relay.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/dns_relay.dir/build.make CMakeFiles/dns_relay.dir/clean
.PHONY : CMakeFiles/dns_relay.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/run.dir

# All Build rule for target.
CMakeFiles/run.dir/all: CMakeFiles/dns_relay.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run.dir/build.make CMakeFiles/run.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run.dir/build.make CMakeFiles/run.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles --progress-num=8 "Built target run"
.PHONY : CMakeFiles/run.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/run.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/run.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles 0
.PHONY : CMakeFiles/run.dir/rule

# Convenience name for target.
run: CMakeFiles/run.dir/rule
.PHONY : run

# codegen rule for target.
CMakeFiles/run.dir/codegen: CMakeFiles/dns_relay.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run.dir/build.make CMakeFiles/run.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles --progress-num=8 "Finished codegen for target run"
.PHONY : CMakeFiles/run.dir/codegen

# clean rule for target.
CMakeFiles/run.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run.dir/build.make CMakeFiles/run.dir/clean
.PHONY : CMakeFiles/run.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/clean-all.dir

# All Build rule for target.
CMakeFiles/clean-all.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/clean-all.dir/build.make CMakeFiles/clean-all.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/clean-all.dir/build.make CMakeFiles/clean-all.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles --progress-num=1 "Built target clean-all"
.PHONY : CMakeFiles/clean-all.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/clean-all.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/clean-all.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles 0
.PHONY : CMakeFiles/clean-all.dir/rule

# Convenience name for target.
clean-all: CMakeFiles/clean-all.dir/rule
.PHONY : clean-all

# codegen rule for target.
CMakeFiles/clean-all.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/clean-all.dir/build.make CMakeFiles/clean-all.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/学习相关文件/大二下/计网/实验/课设/dns_relay_crossplatform/build/CMakeFiles --progress-num=1 "Finished codegen for target clean-all"
.PHONY : CMakeFiles/clean-all.dir/codegen

# clean rule for target.
CMakeFiles/clean-all.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/clean-all.dir/build.make CMakeFiles/clean-all.dir/clean
.PHONY : CMakeFiles/clean-all.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

